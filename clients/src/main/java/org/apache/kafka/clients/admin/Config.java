/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements. See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.kafka.clients.admin;

import org.apache.kafka.common.annotation.InterfaceStability;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * A configuration object containing the configuration entries for a resource.
 * <p>
 * The API of this class is evolving, see {@link Admin} for details.
 */
@InterfaceStability.Evolving
public class Config {

    private final Map<String, ConfigEntry> entries = new HashMap<>();

    /**
     * Create a configuration instance with the provided entries.
     */
    public Config(Collection<ConfigEntry> entries) {
        for (ConfigEntry entry : entries) {
            this.entries.put(entry.name(), entry);
        }
    }

    /**
     * Configuration entries for a resource.
     */
    public Collection<ConfigEntry> entries() {
        return Collections.unmodifiableCollection(entries.values());
    }

    /**
     * Get the configuration entry with the provided name or null if there isn't one.
     */
    public ConfigEntry get(String name) {
        return entries.get(name);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        Config config = (Config) o;

        return entries.equals(config.entries);
    }

    @Override
    public int hashCode() {
        return entries.hashCode();
    }

    @Override
    public String toString() {
        return "Config(entries=" + entries.values() + ")";
    }
}
