/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements. See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.kafka.clients.admin;

import org.apache.kafka.common.annotation.InterfaceStability;

/**
 * Represents information about deleted records
 *
 * The API for this class is still evolving and we may break compatibility in minor releases, if necessary.
 */
@InterfaceStability.Evolving
public class DeletedRecords {

    private final long lowWatermark;

    /**
     * Create an instance of this class with the provided parameters.
     *
     * @param lowWatermark  "low watermark" for the topic partition on which the deletion was executed
     */
    public DeletedRecords(long lowWatermark) {
        this.lowWatermark = lowWatermark;
    }

    /**
     * Return the "low watermark" for the topic partition on which the deletion was executed
     */
    public long lowWatermark() {
        return lowWatermark;
    }
}
