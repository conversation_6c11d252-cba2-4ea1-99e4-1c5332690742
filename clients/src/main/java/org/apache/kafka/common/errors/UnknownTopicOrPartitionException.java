/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements. See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.kafka.common.errors;

/**
 * This topic/partition doesn't exist.
 * This exception is used in contexts where a topic doesn't seem to exist based on possibly stale metadata.
 * This exception is retriable because the topic or partition might subsequently be created.
 *
 * @see InvalidTopicException
 */
public class UnknownTopicOrPartitionException extends InvalidMetadataException {

    private static final long serialVersionUID = 1L;

    public UnknownTopicOrPartitionException() {
    }

    public UnknownTopicOrPartitionException(String message) {
        super(message);
    }

    public UnknownTopicOrPartitionException(Throwable throwable) {
        super(throwable);
    }

    public UnknownTopicOrPartitionException(String message, Throwable throwable) {
        super(message, throwable);
    }

}
