// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

{
  "apiKey": 34,
  "type": "request",
  "name": "AlterReplicaLogDirsRequest",
  // Version 1 is the same as version 0.
  "validVersions": "0-1",
  "flexibleVersions": "none",
  "fields": [
    { "name": "Dirs", "type": "[]AlterReplicaLogDir", "versions": "0+", 
      "about": "The alterations to make for each directory.", "fields": [
      { "name": "Path", "type": "string", "versions": "0+", "mapKey": true,
        "about": "The absolute directory path." },
      { "name": "Topics", "type": "[]AlterReplicaLogDirTopic", "versions": "0+",
        "about": "The topics to add to the directory.",  "fields": [
        { "name": "Name", "type": "string", "versions": "0+", "mapKey": true, "entityType": "topicName",
          "about": "The topic name." },
        { "name": "Partitions", "type": "[]int32", "versions": "0+",
          "about": "The partition indexes." }
      ]}
    ]}
  ]
}
