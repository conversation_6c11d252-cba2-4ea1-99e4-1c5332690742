// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

{
  "apiKey": 7,
  "type": "request",
  "name": "ControlledShutdownRequest",
  // Version 0 of ControlledShutdownRequest has a non-standard request header
  // which does not include clientId.  Version 1 and later use the standard
  // request header.
  //
  // Version 1 is the same as version 0.
  //
  // Version 2 adds BrokerEpoch.
  "validVersions": "0-3",
  "flexibleVersions": "3+",
  "fields": [
    { "name": "BrokerId", "type": "int32", "versions": "0+", "entityType": "brokerId",
      "about": "The id of the broker for which controlled shutdown has been requested." },
    { "name": "BrokerEpoch", "type": "int64", "versions": "2+", "default": "-1", "ignorable": true,
      "about": "The broker epoch." }
  ]
}
