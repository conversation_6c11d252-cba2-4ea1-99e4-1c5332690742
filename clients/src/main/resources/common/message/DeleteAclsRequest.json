// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

{
  "apiKey": 31,
  "type": "request",
  "name": "DeleteAclsRequest",
  // Version 1 adds the pattern type.
  // Version 2 enables flexible versions.
  "validVersions": "0-2",
  "flexibleVersions": "2+",
  "fields": [
    { "name": "Filters", "type": "[]DeleteAclsFilter", "versions": "0+",
      "about": "The filters to use when deleting ACLs.", "fields": [
      { "name": "ResourceTypeFilter", "type": "int8", "versions": "0+",
        "about": "The resource type." },
      { "name": "ResourceNameFilter", "type": "string", "versions": "0+", "nullableVersions": "0+",
        "about": "The resource name." },
      { "name": "PatternTypeFilter", "type": "int8", "versions": "1+", "default": "3", "ignorable": false,
        "about": "The pattern type." },
      { "name": "PrincipalFilter", "type": "string", "versions": "0+", "nullableVersions": "0+",
        "about": "The principal filter, or null to accept all principals." },
      { "name": "HostFilter", "type": "string", "versions": "0+", "nullableVersions": "0+",
        "about": "The host filter, or null to accept all hosts." },
      { "name": "Operation", "type": "int8", "versions": "0+",
        "about": "The ACL operation." },
      { "name": "PermissionType", "type": "int8", "versions": "0+",
        "about": "The permission type." }
    ]}
  ]
}
