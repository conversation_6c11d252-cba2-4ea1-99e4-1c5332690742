// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

{
  "apiKey": 31,
  "type": "response",
  "name": "DeleteAclsResponse",
  // Version 1 adds the resource pattern type.
  // Starting in version 1, on quota violation, brokers send out responses before throttling.
  // Version 2 enables flexible versions.
  "validVersions": "0-2",
  "flexibleVersions": "2+",
  "fields": [
    { "name": "ThrottleTimeMs", "type": "int32", "versions": "0+",
      "about": "The duration in milliseconds for which the request was throttled due to a quota violation, or zero if the request did not violate any quota." },
    { "name": "FilterResults", "type": "[]DeleteAclsFilterResult", "versions": "0+",
      "about": "The results for each filter.", "fields": [
      { "name": "ErrorCode", "type": "int16", "versions": "0+",
        "about": "The error code, or 0 if the filter succeeded." },
      { "name": "ErrorMessage", "type": "string", "versions": "0+", "nullableVersions": "0+",
        "about": "The error message, or null if the filter succeeded." },
      { "name": "MatchingAcls", "type": "[]DeleteAclsMatchingAcl", "versions": "0+",
        "about": "The ACLs which matched this filter.", "fields": [
        { "name": "ErrorCode", "type": "int16", "versions": "0+",
          "about": "The deletion error code, or 0 if the deletion succeeded." },
        { "name": "ErrorMessage", "type": "string", "versions": "0+", "nullableVersions": "0+",
          "about": "The deletion error message, or null if the deletion succeeded." },
        { "name": "ResourceType", "type": "int8", "versions": "0+",
          "about": "The ACL resource type." },
        { "name": "ResourceName", "type": "string", "versions": "0+",
          "about": "The ACL resource name." },
        { "name": "PatternType", "type": "int8", "versions": "1+", "default": "3", "ignorable": false,
          "about": "The ACL resource pattern type." },
        { "name": "Principal", "type": "string", "versions": "0+",
          "about": "The ACL principal." },
        { "name": "Host", "type": "string", "versions": "0+",
          "about": "The ACL host." },
        { "name": "Operation", "type": "int8", "versions": "0+",
          "about": "The ACL operation." },
        { "name": "PermissionType", "type": "int8", "versions": "0+",
          "about": "The ACL permission type." }
      ]}
    ]}
  ]
}
