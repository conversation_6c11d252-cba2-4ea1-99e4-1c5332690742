// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

{
  "apiKey": 21,
  "type": "request",
  "name": "DeleteRecordsRequest",
  // Version 1 is the same as version 0.
  "validVersions": "0-1",
  "flexibleVersions": "none",
  "fields": [
    { "name": "Topics", "type": "[]DeleteRecordsTopic", "versions": "0+",
      "about": "Each topic that we want to delete records from.", "fields": [
      { "name": "Name", "type": "string", "versions": "0+", "entityType": "topicName",
        "about": "The topic name." },
      { "name": "Partitions", "type": "[]DeleteRecordsPartition", "versions": "0+",
        "about": "Each partition that we want to delete records from.", "fields": [
        { "name": "PartitionIndex", "type": "int32", "versions": "0+",
          "about": "The partition index." },
        { "name": "Offset", "type": "int64", "versions": "0+",
          "about": "The deletion offset." }
      ]}
    ]},
    { "name": "TimeoutMs", "type": "int32", "versions": "0+",
      "about": "How long to wait for the deletion to complete, in milliseconds." }
  ]
}
