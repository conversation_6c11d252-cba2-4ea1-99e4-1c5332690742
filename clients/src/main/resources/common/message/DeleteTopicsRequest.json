// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

{
  "apiKey": 20,
  "type": "request",
  "name": "DeleteTopicsRequest",
  // Versions 0, 1, 2, and 3 are the same.
  //
  // Version 4 is the first flexible version.
  "validVersions": "0-4",
  "flexibleVersions": "4+",
  "fields": [
    { "name": "TopicNames", "type": "[]string", "versions": "0+", "entityType": "topicName",
      "about": "The names of the topics to delete" },
    { "name": "TimeoutMs", "type": "int32", "versions": "0+",
      "about": "The length of time in milliseconds to wait for the deletions to complete." }
  ]
}
