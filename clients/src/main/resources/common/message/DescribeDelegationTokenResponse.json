// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

{
  "apiKey": 41,
  "type": "response",
  "name": "DescribeDelegationTokenResponse",
  // Starting in version 1, on quota violation, brokers send out responses before throttling.
  // Version 2 adds flexible version support
  "validVersions": "0-2",
  "flexibleVersions": "2+",
  "fields": [
    { "name": "ErrorCode", "type": "int16", "versions": "0+",
      "about": "The error code, or 0 if there was no error." },
    { "name": "Tokens", "type": "[]DescribedDelegationToken", "versions": "0+",
      "about": "The tokens.", "fields": [
      { "name": "PrincipalType", "type": "string", "versions": "0+",
        "about": "The token principal type." },
      { "name": "PrincipalName", "type": "string", "versions": "0+",
        "about": "The token principal name." },
      { "name": "IssueTimestamp", "type": "int64", "versions": "0+",
        "about": "The token issue timestamp in milliseconds." },
      { "name": "ExpiryTimestamp", "type": "int64", "versions": "0+",
        "about": "The token expiry timestamp in milliseconds." },
      { "name": "MaxTimestamp", "type": "int64", "versions": "0+",
        "about": "The token maximum timestamp length in milliseconds." },
      { "name": "TokenId", "type": "string", "versions": "0+",
        "about": "The token ID." },
      { "name": "Hmac", "type": "bytes", "versions": "0+",
        "about": "The token HMAC." },
      { "name": "Renewers", "type": "[]DescribedDelegationTokenRenewer", "versions": "0+",
        "about": "Those who are able to renew this token before it expires.", "fields": [
        { "name": "PrincipalType", "type": "string", "versions": "0+",
          "about": "The renewer principal type" },
        { "name": "PrincipalName", "type": "string", "versions": "0+",
          "about": "The renewer principal name" }
      ]}
    ]},
    { "name": "ThrottleTimeMs", "type": "int32", "versions": "0+",
      "about": "The duration in milliseconds for which the request was throttled due to a quota violation, or zero if the request did not violate any quota." }
  ]
}
