// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

{
  "apiKey": 43,
  "type": "response",
  "name": "ElectLeadersResponse",
  // Version 1 adds a top-level error code.
  //
  // Version 2 is the first flexible version.
  "validVersions": "0-2",
  "flexibleVersions": "2+",
  "fields": [
    { "name": "ThrottleTimeMs", "type": "int32", "versions": "0+",
      "about": "The duration in milliseconds for which the request was throttled due to a quota violation, or zero if the request did not violate any quota." },
    { "name": "ErrorCode", "type": "int16", "versions": "1+", "ignorable": false,
      "about": "The top level response error code." },
    { "name": "ReplicaElectionResults", "type": "[]ReplicaElectionResult", "versions": "0+",
      "about": "The election results, or an empty array if the requester did not have permission and the request asks for all partitions.", "fields": [
      { "name": "Topic", "type": "string", "versions": "0+", "entityType": "topicName",
        "about": "The topic name" },
      { "name": "PartitionResult", "type": "[]PartitionResult", "versions": "0+",
        "about": "The results for each partition", "fields": [
        { "name": "PartitionId", "type": "int32", "versions": "0+",
          "about": "The partition id" },
        { "name": "ErrorCode", "type": "int16", "versions": "0+",
          "about": "The result error, or zero if there was no error."},
        { "name": "ErrorMessage", "type": "string", "versions": "0+", "nullableVersions": "0+",
          "about": "The result message, or null if there was no error."}
      ]}
    ]}
  ]
}
