// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

{
  "apiKey": 2,
  "type": "response",
  "name": "ListOffsetResponse",
  // Version 1 removes the offsets array in favor of returning a single offset.
  // Version 1 also adds the timestamp associated with the returned offset.
  //
  // Version 2 adds the throttle time.
  //
  // Starting in version 3, on quota violation, brokers send out responses before throttling.
  //
  // Version 4 adds the leader epoch, which is used for fencing.
  //
  // Version 5 adds a new error code, OFFSET_NOT_AVAILABLE.
  "validVersions": "0-5",
  "flexibleVersions": "none",
  "fields": [
    { "name": "ThrottleTimeMs", "type": "int32", "versions": "2+", "ignorable": true,
      "about": "The duration in milliseconds for which the request was throttled due to a quota violation, or zero if the request did not violate any quota." },
    { "name": "Topics", "type": "[]ListOffsetTopicResponse", "versions": "0+",
      "about": "Each topic in the response.", "fields": [
      { "name": "Name", "type": "string", "versions": "0+", "entityType": "topicName",
        "about": "The topic name" },
      { "name": "Partitions", "type": "[]ListOffsetPartitionResponse", "versions": "0+",
        "about": "Each partition in the response.", "fields": [
        { "name": "PartitionIndex", "type": "int32", "versions": "0+",
          "about": "The partition index." },
        { "name": "ErrorCode", "type": "int16", "versions": "0+",
          "about": "The partition error code, or 0 if there was no error." },
        { "name": "OldStyleOffsets", "type": "[]int64", "versions": "0", "ignorable": false,
          "about": "The result offsets." },
        { "name": "Timestamp", "type": "int64", "versions": "1+", "default": "-1", "ignorable": false,
          "about": "The timestamp associated with the returned offset." },
        { "name": "Offset", "type": "int64", "versions": "1+", "default": "-1", "ignorable": false,
          "about": "The returned offset." },
        { "name": "LeaderEpoch", "type": "int32", "versions": "4+" }
      ]}
    ]}
  ]
}
