// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

{
  "apiKey": 23,
  "type": "request",
  "name": "OffsetForLeaderEpochRequest",
  // Version 1 is the same as version 0.
  //
  // Version 2 adds the current leader epoch to support fencing.
  //
  // Version 3 adds ReplicaId (the default is -2 which conventionally represents a
  //    "debug" consumer which is allowed to see offsets beyond the high watermark).
  //    Followers will use this replicaId when using an older version of the protocol.
  "validVersions": "0-3",
  "flexibleVersions": "none",
  "fields": [
    { "name": "ReplicaId", "type": "int32", "versions": "3+", "default": -2, "ignorable": true,
      "about": "The broker ID of the follower, of -1 if this request is from a consumer." },
    { "name": "Topics", "type": "[]OffsetForLeaderTopic", "versions": "0+",
      "about": "Each topic to get offsets for.", "fields": [
      { "name": "Name", "type": "string", "versions": "0+", "entityType": "topicName",
        "about": "The topic name." },
      { "name": "Partitions", "type": "[]OffsetForLeaderPartition", "versions": "0+",
        "about": "Each partition to get offsets for.", "fields": [
        { "name": "PartitionIndex", "type": "int32", "versions": "0+",
          "about": "The partition index." },
        { "name": "CurrentLeaderEpoch", "type": "int32", "versions": "2+", "default": "-1", "ignorable": true,
          "about": "An epoch used to fence consumers/replicas with old metadata.  If the epoch provided by the client is larger than the current epoch known to the broker, then the UNKNOWN_LEADER_EPOCH error code will be returned. If the provided epoch is smaller, then the FENCED_LEADER_EPOCH error code will be returned." },
        { "name": "LeaderEpoch", "type": "int32", "versions": "0+",
          "about": "The epoch to look up an offset for." }
      ]}
    ]}
  ]
}
