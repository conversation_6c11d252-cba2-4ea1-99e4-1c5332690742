// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

{
  "apiKey": 23,
  "type": "response",
  "name": "OffsetForLeaderEpochResponse",
  // Version 1 added the leader epoch to the response.
  // Version 2 added the throttle time.
  "validVersions": "0-3",
  "flexibleVersions": "none",
  "fields": [
    { "name": "ThrottleTimeMs", "type": "int32", "versions": "2+", "ignorable": true,
      "about": "The duration in milliseconds for which the request was throttled due to a quota violation, or zero if the request did not violate any quota." },
    { "name": "Topics", "type": "[]OffsetForLeaderTopicResult", "versions": "0+",
      "about": "Each topic we fetched offsets for.", "fields": [
      { "name": "Name", "type": "string", "versions": "0+", "entityType": "topicName",
        "about": "The topic name." },
      { "name": "Partitions", "type": "[]OffsetForLeaderPartitionResult", "versions": "0+",
        "about": "Each partition in the topic we fetched offsets for.", "fields": [
        { "name": "ErrorCode", "type": "int16", "versions": "0+",
          "about": "The error code 0, or if there was no error." },
        { "name": "PartitionIndex", "type": "int32", "versions": "0+",
          "about": "The partition index." },
        { "name": "LeaderEpoch", "type": "int32", "versions": "1+", "default": "-1", "ignorable": true,
          "about": "The leader epoch of the partition." },
        { "name": "EndOffset", "type": "int64", "versions": "0+",
          "about": "The end offset of the epoch." }
      ]}
    ]}
  ]
}
