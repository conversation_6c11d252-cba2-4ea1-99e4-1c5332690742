<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
 contributor license agreements.  See the NOTICE file distributed with
 this work for additional information regarding copyright ownership.
 The ASF licenses this file to You under the Apache License, Version 2.0
 (the "License"); you may not use this file except in compliance with
 the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<script><!--#include virtual="js/templateData.js" --></script>

<!--#include virtual="../includes/_header.htm" -->
<!--#include virtual="../includes/_top.htm" -->


<div class="content documentation documentation--current">
	<!--#include virtual="../includes/_nav.htm" -->
	<div class="right">
		<!--#include virtual="../includes/_docs_banner.htm" -->
    <h1>Documentation</h1>
    <h3>Kafka 2.5 Documentation</h3>
    Prior releases: <a href="/07/documentation.html">0.7.x</a>, <a href="/08/documentation.html">0.8.0</a>, <a href="/081/documentation.html">0.8.1.X</a>, <a href="/082/documentation.html">0.8.2.X</a>, <a href="/090/documentation.html">0.9.0.X</a>, <a href="/0100/documentation.html">0.10.0.X</a>, <a href="/0101/documentation.html">0.10.1.X</a>, <a href="/0102/documentation.html">0.10.2.X</a>, <a href="/0110/documentation.html">0.11.0.X</a>, <a href="/10/documentation.html">1.0.X</a>, <a href="/11/documentation.html">1.1.X</a>, <a href="/20/documentation.html">2.0.X</a>, <a href="/21/documentation.html">2.1.X</a>, <a href="/22/documentation.html">2.2.X</a>, <a href="/23/documentation.html">2.3.X</a>, <a href="/24/documentation.html">2.4.X</a>.

    <!--#include virtual="toc.html" -->

    <h2><a id="gettingStarted" href="#gettingStarted">1. Getting Started</a></h2>
      <h3><a id="introduction" href="#introduction">1.1 Introduction</a></h3>
      <!--#include virtual="introduction.html" -->
      <h3><a id="uses" href="#uses">1.2 Use Cases</a></h3>
      <!--#include virtual="uses.html" -->
      <h3><a id="quickstart" href="#quickstart">1.3 Quick Start</a></h3>
      <!--#include virtual="quickstart.html" -->
      <h3><a id="ecosystem" href="#ecosystem">1.4 Ecosystem</a></h3>
      <!--#include virtual="ecosystem.html" -->
      <h3><a id="upgrade" href="#upgrade">1.5 Upgrading From Previous Versions</a></h3>
      <!--#include virtual="upgrade.html" -->

    <h2><a id="api" href="#api">2. APIs</a></h2>

    <!--#include virtual="api.html" -->

    <h2><a id="configuration" href="#configuration">3. Configuration</a></h2>

    <!--#include virtual="configuration.html" -->

    <h2><a id="design" href="#design">4. Design</a></h2>

    <!--#include virtual="design.html" -->

    <h2><a id="implementation" href="#implementation">5. Implementation</a></h2>

    <!--#include virtual="implementation.html" -->

    <h2><a id="operations" href="#operations">6. Operations</a></h2>

    <!--#include virtual="ops.html" -->

    <h2><a id="security" href="#security">7. Security</a></h2>
    <!--#include virtual="security.html" -->

    <h2><a id="connect" href="#connect">8. Kafka Connect</a></h2>
    <!--#include virtual="connect.html" -->

    <h2><a id="streams" href="/documentation/streams">9. Kafka Streams</a></h2>
    <p>
        Kafka Streams is a client library for processing and analyzing data stored in Kafka. It builds upon important stream processing concepts such as properly distinguishing between event time and processing time, windowing support, exactly-once processing semantics and simple yet efficient management of application state.
    </p>
    <p>
        Kafka Streams has a <b>low barrier to entry</b>: You can quickly write and run a small-scale proof-of-concept on a single machine; and you only need to run additional instances of your application on multiple machines to scale up to high-volume production workloads. Kafka Streams transparently handles the load balancing of multiple instances of the same application by leveraging Kafka's parallelism model.
    </p>

    <p>Learn More about Kafka Streams read <a href="/documentation/streams">this</a> Section.</p>

<!--#include virtual="../includes/_footer.htm" -->
<!--#include virtual="../includes/_docs_footer.htm" -->
