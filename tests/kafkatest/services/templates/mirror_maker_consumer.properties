# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# see kafka.consumer.ConsumerConfig for more details

bootstrap.servers={{ source.bootstrap_servers(security_config.security_protocol) }}

{% if source_auto_offset_reset is defined and source_auto_offset_reset is not none %}
auto.offset.reset={{ source_auto_offset_reset|default('latest') }}
{% endif %}

group.id={{ group_id|default('test-consumer-group') }}

{% if partition_assignment_strategy is defined and partition_assignment_strategy is not none %}
partition.assignment.strategy={{ partition_assignment_strategy }}
{% endif %}
